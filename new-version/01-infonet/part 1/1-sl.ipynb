# Import necessary libraries
import os
import csv
import time
from urllib.parse import urlparse
from parsel import Selector
import undetected_chromedriver as uc
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By
from selenium.common.exceptions import TimeoutException

chrome_options = uc.ChromeOptions()
options = [
    '--no-sandbox',
    '--start-maximized',
    '--disable-extensions',
    '--ignore-certificate-errors',
    '--disable-blink-features=AutomationControlled',
    '--disable-infobars',
    '--disable-dev-shm-usage',
    '--disable-browser-side-navigation',
    '--disable-gpu'
]

for option in options:
    chrome_options.add_argument(option)

driver = uc.Chrome(options=chrome_options)

# Set page load timeout to 3 seconds
driver.set_page_load_timeout(5)

# Initialize WebDriverWait with a 30-second timeout
driver_wait = WebDriverWait(driver, 30)

INPUT_FILE = "extrait_10001_20001_part_1.csv"
OUTPUT_FILE = "part-1-data.csv"

RAW_NUMBER = 0
SKIP_RAW = 94  # Start from beginning

# Read the CSV file
data_rows = []

with open(INPUT_FILE, 'r', encoding='utf-8') as file:
    csv_reader = csv.DictReader(file)
    headers = csv_reader.fieldnames
    
    for row in csv_reader:
        data_rows.append(row)

# Function to detect captcha
def detect_captcha(page_source):
    """
    Detect if the current page contains a captcha or security check
    Only triggers on the specific captcha message
    """
    # Only look for the specific captcha message you mentioned
    captcha_text = "Let's confirm you are human"
    
    if captcha_text in page_source:
        return True
    return False

def handle_captcha_manual():
    """
    Handle captcha by pausing and waiting for manual intervention
    """
    print("\n" + "="*60)
    print("🚨 CAPTCHA DETECTED! 🚨")
    print("="*60)
    print("A captcha or security check has been detected.")
    print("Please solve the captcha manually in the browser window.")
    print("After solving the captcha, press ENTER to continue...")
    print("="*60)
    
    # Wait for user input
    input("Press ENTER after solving the captcha: ")
    
    print("✅ Resuming script...")
    print("="*60 + "\n")

# Prepare CSV file with headers (create/overwrite the file)
original_fieldnames = list(data_rows[0].keys()) if data_rows else []
new_fieldnames = ['website', 'phone', "email"]
all_fieldnames = original_fieldnames + new_fieldnames

# Create CSV file and write header
with open(OUTPUT_FILE, 'w', newline='', encoding='utf-8') as csvfile:
    writer = csv.DictWriter(csvfile, fieldnames=all_fieldnames)
    writer.writeheader()


# Process each row and save immediately
processed_count = 0
for index, row in enumerate(data_rows):
    if index < SKIP_RAW:
        continue

    siret = row['siret']
    denominationUniteLegale = row['denominationUniteLegale']
    verif_url = f"https://infonet.fr/entreprises/{siret}-{denominationUniteLegale.replace(' ', '-')}"
    
    
    
    
    







    
    current_row = index + 1
    print(f"Processing Raw Number: {RAW_NUMBER} ...")
    RAW_NUMBER += 1
    

    # Initialize default values
    website = None
    phone = None
    email = None
    

    try:
        # Navigate to the URL
        driver.get(verif_url)
    except:
        pass

    time.sleep(1)

    # Get page source for captcha detection
    html = driver.page_source
    
    # Check for captcha before proceeding
    if detect_captcha(html):
        handle_captcha_manual()
        # Get fresh page source after captcha is solved
        time.sleep(2)
        html = driver.page_source
    
    selector = Selector(html)

    phone = selector.xpath("//span[@id='header-company-phone']//a[starts-with(@href, 'tel:')]/text()").get()
    email = selector.xpath("//span[@id='header-company-email']//a[starts-with(@href, 'mailto:')]/text()").get()
    website = selector.xpath("//span[@id='header-company-website']//a/@href").get()

    # Prepare the result row (combining original row data with new extracted data)
    result_row = row.copy()
    result_row.update({
        'website': website,
        'phone': phone,
        "email": email
    })
    
    
    # Save immediately to CSV file (append mode)
    try:
        with open(OUTPUT_FILE, 'a', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=all_fieldnames)
            writer.writerow(result_row)
        processed_count += 1
    except Exception as e:
        print(f"  ✗ Error saving to CSV: {str(e)}")

print(f"\nProcessing complete! Processed and saved {processed_count} records to {OUTPUT_FILE}.")  



